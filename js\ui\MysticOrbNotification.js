// ===== MYSTIC ORB ACQUISITION NOTIFICATION =====
// Displays an overlay when a mystic orb is obtained

class MysticOrbNotification {
    constructor() {
        this.modal = null;
        this.isShowing = false;
        this.imageLoaded = false;
        this.init();
    }
    
    init() {
        // Get the modal element
        this.modal = document.getElementById('mysticOrbModal');
        if (!this.modal) {
            console.error('Mystic orb modal not found in DOM');
            return;
        }
        
        // Set up click handler to dismiss the notification
        this.modal.addEventListener('click', () => {
            this.dismissNotification();
        });
        
        // Preload the mystic orb image
        this.preloadImage();
        
        console.log('Mystic orb notification system initialized');
    }
    
    // Preload the mystic orb image
    async preloadImage() {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                this.imageLoaded = true;
                console.log('Mystic orb image preloaded successfully');
                resolve();
            };
            img.onerror = () => {
                console.warn('Failed to preload mystic orb image');
                this.imageLoaded = false;
                resolve(); // Still resolve to prevent blocking
            };
            img.src = 'assets/images/orb special.png';
        });
    }
    
    // Show the mystic orb notification
    async showNotification() {
        if (!this.modal || this.isShowing) return;
        
        console.log('Showing mystic orb notification');
        
        // Ensure the image is loaded before showing
        if (!this.imageLoaded) {
            await this.preloadImage();
        }
        
        // Show the modal
        this.modal.classList.add('active');
        this.isShowing = true;
        
        // Add a slight delay for better visual effect
        setTimeout(() => {
            if (this.modal) {
                this.modal.style.opacity = '1';
            }
        }, 50);
        
        // Auto-dismiss after 5 seconds if user doesn't click
        setTimeout(() => {
            if (this.isShowing) {
                this.dismissNotification();
            }
        }, 5000);
    }
    
    // Dismiss the mystic orb notification
    dismissNotification() {
        if (!this.modal || !this.isShowing) return;
        
        console.log('Dismissing mystic orb notification');
        
        // Fade out effect
        this.modal.style.opacity = '0';
        
        // Hide the modal after fade out
        setTimeout(() => {
            if (this.modal) {
                this.modal.classList.remove('active');
                this.isShowing = false;
            }
        }, 300);
    }
}

// Initialize the mystic orb notification system when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.mysticOrbNotification = new MysticOrbNotification();
});
