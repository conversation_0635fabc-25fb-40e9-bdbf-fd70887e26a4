// ===== INPUT HANDLER =====
// Keyboard and mouse input management

class InputHandler {
    constructor() {
        this.setupEventListeners();
    }
    
    // Setup all input event listeners
    setupEventListeners() {
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // Mouse click events for enemy turn skipping
        document.addEventListener('click', (e) => this.handleMouseClick(e));
        
        // Button click events
        this.setupButtonEvents();
    }
    
    // Handle keyboard input
    handleKeyDown(e) {
        // Combat input
        if (gameState.inCombat) {
            this.handleCombatInput(e);
            return;
        }
        
        // Movement input
        this.handleMovementInput(e);
        
        // Interaction input
        this.handleInteractionInput(e);
    }
    
    // Handle combat keyboard input
    handleCombatInput(e) {
        // Check if it's an enemy turn waiting for player input
        if (combatSystem.waitingForPlayerInput && e.key === 'Enter') {
            combatSystem.skipEnemyThinking();
            return;
        }
        
        // Regular combat actions
        switch(e.key) {
            case '1':
                combatSystem.playerAction('attack');
                break;
            case '2':
                combatSystem.playerAction('magic');
                break;
            case '3':
                combatSystem.playerAction('defend');
                break;
            case '4':
                combatSystem.playerAction('flee');
                break;
            case '5':
                combatSystem.playerAction('concentrate');
                break;
            case '6':
                combatSystem.playerAction('skill');
                break;
        }
    }
    
    // Handle movement keyboard input
    handleMovementInput(e) {
        switch(e.key) {
            case 'ArrowUp':
            case 'w':
            case 'W':
                player.moveForward();
                break;
            case 'ArrowDown':
            case 's':
            case 'S':
                player.moveBackward();
                break;
            case 'ArrowLeft':
            case 'a':
            case 'A':
                player.turnLeft();
                break;
            case 'ArrowRight':
            case 'd':
            case 'D':
                player.turnRight();
                break;
        }
    }
    
    // Handle interaction keyboard input
    handleInteractionInput(e) {
        if (e.key === 'Enter') {
            const currentCell = gameState.getCurrentCell();
            const facingCell = gameState.getFacingCell();
            
            // Check for interactions with current cell first
            switch(currentCell.type) {
                case 'stairs':
                    player.descendStairs();
                    return;
                case 'heal':
                    player.useHealPoint();
                    return;
            }
            
            // Check for interactions with facing cell
            if (facingCell && facingCell.type === 'door') {
                player.openDoor();
            }
        }
    }
    
    // Handle mouse click input
    handleMouseClick(e) {
        // Only handle clicks during combat and enemy turns waiting for player input
        if (gameState.inCombat && combatSystem.waitingForPlayerInput) {
            // Prevent default button clicks from also triggering enemy turn skip
            if (e.target.tagName !== 'BUTTON' && !e.target.closest('button')) {
                combatSystem.skipEnemyThinking();
            }
        }
    }
    
    // Setup button click events
    setupButtonEvents() {
        // Movement buttons
        this.addButtonEvent('moveForward', () => player.moveForward());
        this.addButtonEvent('turnLeft', () => player.turnLeft());
        this.addButtonEvent('turnRight', () => player.turnRight());
        this.addButtonEvent('moveBackward', () => player.moveBackward());
        
        // Interaction buttons
        this.addButtonEvent('descendBtn', () => player.descendStairs());
        this.addButtonEvent('doorBtn', () => player.openDoor());
        this.addButtonEvent('healBtn', () => player.useHealPoint());
        
        // Combat buttons are handled by HTML onclick attributes to avoid conflicts
        // No need to add event listeners for combat buttons here
        
        // UI buttons
        this.addButtonEvent('closeLevelUp', () => characterSystem.closeLevelUp());
        this.addButtonEvent('confirmOpenDoor', () => player.confirmOpenDoor());
        this.addButtonEvent('closeDoorUI', () => player.closeDoorUI());
    }
    
    // Helper method to add button event listener
    addButtonEvent(id, callback) {
        const button = document.getElementById(id);
        if (button) {
            button.addEventListener('click', callback);
        }
    }
    
    // Add click events for elements that use onclick attributes
    setupOnClickEvents() {
        // These will be handled by the existing onclick attributes in HTML
        // This method exists for future expansion if needed
    }
    
    // Enable/disable input during certain game states
    setInputEnabled(enabled) {
        this.inputEnabled = enabled;
    }
    
    // Check if input should be processed
    shouldProcessInput() {
        return this.inputEnabled !== false; // Default to true
    }
}

// Create global instance
const inputHandler = new InputHandler();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.inputHandler = inputHandler;
}
