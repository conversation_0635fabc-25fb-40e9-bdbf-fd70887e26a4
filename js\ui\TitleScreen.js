// ===== TITLE SCREEN =====
// Manages the title screen display and transition to main game

class TitleScreen {
    constructor() {
        this.isShowing = false;
        this.imageLoaded = false;
        this.titleScreenElement = null;
        this.musicLoaded = false;
        this.musicStarted = false;
        this.stage = 'initial'; // 'initial' or 'ready'
        this.userInteractionReceived = false;
        this.pushAnyKeyElement = null;
        this.gameStartElement = null;
    }
    
    // Initialize the title screen
    initialize() {
        console.log('TitleScreen: Initializing...');
        this.createTitleScreenHTML();
        this.preloadImage();
    }
    
    // Create the title screen HTML structure
    createTitleScreenHTML() {
        console.log('TitleScreen: Creating HTML structure...');

        // Remove existing title screen if it exists
        const existing = document.getElementById('titleScreen');
        if (existing) {
            existing.remove();
        }

        // Create title screen container
        const titleScreen = document.createElement('div');
        titleScreen.id = 'titleScreen';
        titleScreen.className = 'title-screen';
        titleScreen.style.display = 'none'; // Hidden by default
        
        // Create background image container
        const backgroundContainer = document.createElement('div');
        backgroundContainer.className = 'title-background';
        
        // Create title text
        const titleText = document.createElement('div');
        titleText.className = 'title-text';
        titleText.textContent = 'DungeonRPG';
        
        // Create subtitle
        const subtitle = document.createElement('div');
        subtitle.className = 'title-subtitle';
        subtitle.textContent = 'A Retro 3D Dungeon Adventure';
        
        // Create PUSH ANY KEY button (Stage 1)
        const pushAnyKeyButton = document.createElement('button');
        pushAnyKeyButton.className = 'title-push-key-button';
        pushAnyKeyButton.textContent = 'PUSH ANY KEY';
        pushAnyKeyButton.onclick = () => this.handleUserInteraction();
        this.pushAnyKeyElement = pushAnyKeyButton;

        // Create GAME START button (Stage 2) - initially hidden
        const gameStartButton = document.createElement('button');
        gameStartButton.className = 'title-start-button';
        gameStartButton.textContent = 'GAME START';
        gameStartButton.onclick = () => this.startGame();
        gameStartButton.style.display = 'none';
        this.gameStartElement = gameStartButton;

        // Create OPTION button (Stage 2) - initially hidden
        const optionButton = document.createElement('button');
        optionButton.className = 'title-option-button';
        optionButton.textContent = 'OPTION';
        optionButton.onclick = () => this.showOptionScreen();
        optionButton.style.display = 'none';
        this.optionElement = optionButton;

        // Create button container for GAME START and OPTION buttons
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'title-button-container';
        buttonContainer.appendChild(gameStartButton);
        buttonContainer.appendChild(optionButton);

        // Create content container for text and buttons
        const contentContainer = document.createElement('div');
        contentContainer.className = 'title-content';
        contentContainer.appendChild(titleText);
        contentContainer.appendChild(subtitle);
        contentContainer.appendChild(pushAnyKeyButton);
        contentContainer.appendChild(buttonContainer);
        
        // Assemble the title screen
        titleScreen.appendChild(backgroundContainer);
        titleScreen.appendChild(contentContainer);
        
        // Add to document body
        document.body.appendChild(titleScreen);
        this.titleScreenElement = titleScreen;

        // Add keyboard event listener for "any key" functionality
        this.keyboardHandler = (event) => {
            if (this.stage === 'initial' && this.isShowing) {
                this.handleUserInteraction();
            }
        };

        // Add click event listener for "any click" functionality
        this.clickHandler = (event) => {
            if (this.stage === 'initial' && this.isShowing) {
                // Only handle clicks outside of buttons to avoid double-triggering
                if (!event.target.classList.contains('title-push-key-button')) {
                    this.handleUserInteraction();
                }
            }
        };

        console.log('TitleScreen: HTML structure created and added to DOM');
    }
    
    // Preload the title image
    async preloadImage() {
        const imagePath = 'assets/images/title.png';
        
        if (imageManager) {
            try {
                const result = await imageManager.preloadImage(imagePath);
                if (result.success) {
                    this.imageLoaded = true;
                    console.log('Title image preloaded successfully');
                    this.updateBackgroundImage();
                } else {
                    console.warn('Failed to preload title image:', imagePath);
                    this.imageLoaded = false;
                }
                return result;
            } catch (error) {
                console.error('Error preloading title image:', error);
                this.imageLoaded = false;
                return { success: false, error };
            }
        } else {
            // Fallback if imageManager is not available
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = () => {
                    this.imageLoaded = true;
                    console.log('Title image preloaded successfully (fallback)');
                    this.updateBackgroundImage();
                    resolve({ success: true });
                };
                img.onerror = () => {
                    console.warn('Failed to preload title image (fallback)');
                    this.imageLoaded = false;
                    resolve({ success: false });
                };
                img.src = imagePath;
            });
        }
    }
    
    // Update background image when loaded
    updateBackgroundImage() {
        if (this.titleScreenElement && this.imageLoaded) {
            const backgroundContainer = this.titleScreenElement.querySelector('.title-background');
            if (backgroundContainer) {
                backgroundContainer.style.backgroundImage = 'url(assets/images/title.png)';
            }
        }
    }
    
    // Show the title screen
    show() {
        console.log('TitleScreen: Attempting to show title screen...');
        if (this.titleScreenElement) {
            this.titleScreenElement.style.display = 'flex';
            this.titleScreenElement.classList.remove('fade-out'); // Ensure full opacity
            this.isShowing = true;
            this.stage = 'initial';
            console.log('TitleScreen: Title screen displayed successfully');

            // Add event listeners for user interaction
            document.addEventListener('keydown', this.keyboardHandler);
            document.addEventListener('click', this.clickHandler);

            // Reset UI to initial state
            this.showInitialStage();
        } else {
            console.error('TitleScreen: titleScreenElement is null!');
        }
    }
    
    // Hide the title screen
    hide() {
        if (this.titleScreenElement) {
            this.titleScreenElement.style.display = 'none';
            this.titleScreenElement.classList.remove('fade-out'); // Reset fade-out class
            this.isShowing = false;
            console.log('Title screen hidden');
        }

        // Remove event listeners
        document.removeEventListener('keydown', this.keyboardHandler);
        document.removeEventListener('click', this.clickHandler);

        // Ensure background music is stopped when hiding
        if (this.musicStarted) {
            this.stopBackgroundMusic();
        }
    }
    
    // Handle START button click
    startGame() {
        console.log('START button clicked - transitioning to main game');

        // Stop background music immediately
        this.stopBackgroundMusic();

        // Start fade-out animation
        this.fadeOutAndTransition();
    }

    // Handle OPTION button click
    showOptionScreen() {
        console.log('OPTION button clicked - showing option screen');

        // Hide title screen temporarily
        this.titleScreenElement.style.display = 'none';

        // Show option screen
        if (typeof optionScreen !== 'undefined') {
            optionScreen.show();
        } else {
            console.error('OptionScreen not available');
            // Fallback: show title screen again
            this.titleScreenElement.style.display = 'flex';
        }
    }

    // Return from option screen to title screen
    returnFromOptionScreen() {
        console.log('TitleScreen: Returning from option screen');

        // Ensure we're in the ready stage (showing GAME START and OPTION buttons)
        if (this.stage === 'ready') {
            this.titleScreenElement.style.display = 'flex';
            this.titleScreenElement.classList.remove('fade-out');
            this.isShowing = true;

            // Re-add event listeners
            document.addEventListener('keydown', this.keyboardHandler);
            document.addEventListener('click', this.clickHandler);

            console.log('TitleScreen: Successfully returned from option screen');
        } else {
            console.warn('TitleScreen: Cannot return from option screen - not in ready stage');
        }
    }

    // Fade out title screen and transition to main game
    fadeOutAndTransition() {
        if (!this.titleScreenElement) {
            console.error('TitleScreen: titleScreenElement is null during fade-out!');
            return;
        }

        console.log('TitleScreen: Starting fade-out animation...');

        // Add fade-out class to trigger CSS transition
        this.titleScreenElement.classList.add('fade-out');

        // Wait for the transition to complete before hiding and showing main game
        setTimeout(() => {
            console.log('TitleScreen: Fade-out animation completed');

            // Hide title screen completely
            this.hide();

            // Show main game interface
            this.showMainGameInterface();

            // Notify GameCore that we're ready to start
            if (typeof gameCore !== 'undefined' && gameCore.onTitleScreenComplete) {
                gameCore.onTitleScreenComplete();
            }
        }, 1200); // Match the CSS transition duration (1.2s)
    }
    
    // Show the main game interface
    showMainGameInterface() {
        // Show the main game container with fade-in animation
        const gameContainer = document.querySelector('.game-container');
        if (gameContainer) {
            // Set initial state for fade-in (CSS handles display: flex)
            gameContainer.classList.add('fade-in');

            // Force a reflow to ensure the fade-in class is applied
            gameContainer.offsetHeight;

            // Start the fade-in animation
            setTimeout(() => {
                gameContainer.classList.add('show');
            }, 50); // Small delay to ensure smooth transition
        }

        // Ensure canvas and UI are visible
        const canvas = document.getElementById('gameCanvas');
        if (canvas) {
            canvas.style.display = 'block';
        }

        // Trigger initial render if renderer is available
        if (typeof renderer !== 'undefined' && renderer.render) {
            renderer.render();
        }

        // Update UI if uiManager is available
        if (typeof uiManager !== 'undefined' && uiManager.updateUI) {
            uiManager.updateUI();
        }
    }
    
    // Check if title screen is currently showing
    isCurrentlyShowing() {
        return this.isShowing;
    }

    // Show initial stage (PUSH ANY KEY)
    showInitialStage() {
        if (this.pushAnyKeyElement) {
            this.pushAnyKeyElement.style.display = 'block';
        }
        if (this.gameStartElement) {
            this.gameStartElement.style.display = 'none';
        }
        if (this.optionElement) {
            this.optionElement.style.display = 'none';
        }
        this.stage = 'initial';
        console.log('TitleScreen: Showing initial stage (PUSH ANY KEY)');
    }

    // Handle user interaction (any key press or click)
    async handleUserInteraction() {
        if (this.userInteractionReceived) {
            return; // Already handled
        }

        console.log('TitleScreen: User interaction received');
        this.userInteractionReceived = true;

        // Transition to stage 2
        this.showReadyStage();

        // Start background music (HTML5 Audio doesn't require audio context initialization)
        this.startBackgroundMusic();
    }

    // Show ready stage (GAME START + music)
    showReadyStage() {
        if (this.pushAnyKeyElement) {
            this.pushAnyKeyElement.style.display = 'none';
        }
        if (this.gameStartElement) {
            this.gameStartElement.style.display = 'block';
        }
        if (this.optionElement) {
            this.optionElement.style.display = 'block';
        }
        this.stage = 'ready';
        console.log('TitleScreen: Showing ready stage (GAME START)');
    }

    // Start background music
    async startBackgroundMusic() {
        if (this.musicStarted) {
            console.log('TitleScreen: Background music already started');
            return;
        }

        if (typeof soundManager !== 'undefined' && soundManager.playBackgroundMusic) {
            try {
                console.log('TitleScreen: Starting background music...');

                // Verify audio file exists first
                const audioPath = 'assets/audio/Overture.m4a';
                if (soundManager.verifyAudioFile) {
                    const fileCheck = await soundManager.verifyAudioFile(audioPath);
                    if (!fileCheck.success) {
                        console.warn('TitleScreen: Audio file verification failed:', fileCheck.error);
                        this.musicLoaded = false;
                        return;
                    }
                }

                const result = await soundManager.playBackgroundMusic(audioPath);

                if (result.success) {
                    this.musicStarted = true;
                    this.musicLoaded = true;
                    console.log('TitleScreen: Background music started successfully');
                } else {
                    console.warn('TitleScreen: Failed to start background music:', result.error);
                    this.musicLoaded = false;
                }
            } catch (error) {
                console.error('TitleScreen: Error starting background music:', error);
                this.musicLoaded = false;
            }
        } else {
            console.warn('TitleScreen: SoundManager not available or does not support background music');
        }
    }

    // Stop background music
    stopBackgroundMusic() {
        if (typeof soundManager !== 'undefined' && soundManager.stopBackgroundMusic) {
            try {
                console.log('TitleScreen: Stopping background music...');
                soundManager.stopBackgroundMusic();
                this.musicStarted = false;
                console.log('TitleScreen: Background music stopped');
            } catch (error) {
                console.error('TitleScreen: Error stopping background music:', error);
            }
        }
    }
}

// Create global instance
const titleScreen = new TitleScreen();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.titleScreen = titleScreen;
}

// Test function to manually show title screen
window.testTitleScreen = function() {
    console.log('Testing title screen...');
    titleScreen.initialize();
    titleScreen.show();
};
