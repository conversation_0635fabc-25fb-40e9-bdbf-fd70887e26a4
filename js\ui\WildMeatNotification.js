// ===== WILD MEAT ACQUISITION NOTIFICATION =====
// Displays an overlay when wild meat is obtained from beast monsters

class WildMeatNotification {
    constructor() {
        this.modal = null;
        this.isShowing = false;
        this.imageLoaded = false;
        this.init();
    }
    
    init() {
        // Get the modal element
        this.modal = document.getElementById('wildMeatModal');
        if (!this.modal) {
            console.error('Wild meat modal not found in DOM');
            return;
        }
        
        // Set up click handler to dismiss the notification
        this.modal.addEventListener('click', () => {
            this.dismissNotification();
        });
        
        // Preload the wild meat image
        this.preloadImage();
        
        console.log('Wild meat notification system initialized');
    }
    
    // Preload the wild meat image
    async preloadImage() {
        try {
            const img = new Image();
            img.onload = () => {
                this.imageLoaded = true;
                console.log('Wild meat image preloaded successfully');
            };
            img.onerror = () => {
                console.warn('Failed to preload wild meat image');
                this.imageLoaded = false;
            };
            img.src = 'assets/images/wild meat.png';
        } catch (error) {
            console.error('Error preloading wild meat image:', error);
            this.imageLoaded = false;
        }
    }
    
    // Show the wild meat notification
    async showNotification() {
        if (!this.modal || this.isShowing) return;
        
        console.log('Showing wild meat notification');
        
        // Ensure the image is loaded before showing
        if (!this.imageLoaded) {
            await this.preloadImage();
        }
        
        // Show the modal
        this.modal.classList.add('active');
        this.isShowing = true;
        
        // Add a slight delay for better visual effect
        setTimeout(() => {
            if (this.modal) {
                this.modal.style.opacity = '1';
            }
        }, 50);
        
        // Auto-dismiss after 5 seconds if user doesn't click
        setTimeout(() => {
            if (this.isShowing) {
                this.dismissNotification();
            }
        }, 5000);
    }
    
    // Dismiss the notification
    dismissNotification() {
        if (!this.modal || !this.isShowing) return;
        
        console.log('Dismissing wild meat notification');
        
        // Fade out effect
        this.modal.style.opacity = '0';
        
        // Hide the modal after fade out
        setTimeout(() => {
            if (this.modal) {
                this.modal.classList.remove('active');
                this.isShowing = false;
            }
        }, 300);
    }
    
    // Check if notification is currently showing
    isNotificationShowing() {
        return this.isShowing;
    }

    // Debug method to test the notification
    testNotification() {
        console.log('Testing wild meat notification...');
        this.showNotification();
    }
}

// Create global instance
const wildMeatNotification = new WildMeatNotification();
