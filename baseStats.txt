// Character Classes
    CLASSES: {
        WARRIOR: {
            name: 'Warrior',
            life: 120,
            str: 25,
            int: 10,
            agl: 16
        },
        MAGE: {
            name: '<PERSON><PERSON>',
            life: 90,
            str: 12,
            int: 32,
            agl: 12
        },
        RANGER: {
            name: 'Ranger',
            life: 100,
            str: 18,
            int: 15,
            agl: 25
        }


Combat.js
        ally.expReward = Math.floor((ally.maxLife + ally.str + ally.int + ally.agl) * gameState.currentFloor * 0.5);

Character.js
member.expToNext = Math.floor(member.expToNext * 1.5)
