// ===== MAIN ENTRY POINT =====
// Module loading and game initialization

// Global wrapper functions for HTML onclick events
function moveForward() {
    if (window.player) player.moveForward();
}

function moveBackward() {
    if (window.player) player.moveBackward();
}

function turnLeft() {
    if (window.player) player.turnLeft();
}

function turnRight() {
    if (window.player) player.turnRight();
}

function descendStairs() {
    if (window.player) player.descendStairs();
}

function openDoor() {
    if (window.player) player.openDoor();
}

function useHealPoint() {
    if (window.player) player.useHealPoint();
}

function confirmOpenDoor() {
    if (window.player) player.confirmOpenDoor();
}

function closeDoorUI() {
    if (window.player) player.closeDoorUI();
}

function closeLevelUp() {
    if (window.characterSystem) characterSystem.closeLevelUp();
}

// Combat action wrapper
function playerAction(action) {
    if (window.combatSystem) {
        combatSystem.playerAction(action);
    }
}

// Shop system wrappers
function initiatePurchase() {
    if (window.player) player.initiatePurchase();
}

function closeShopUI() {
    if (window.player) player.closeShopUI();
}

function cancelPartySelection() {
    if (window.player) player.cancelPartySelection();
}

// Item system wrappers
function useItem() {
    if (window.player) player.useItem();
}

function closeItemUI() {
    if (window.player) player.closeItemUI();
}

// Shop system wrappers
function showStatEnhancement() {
    if (window.player) player.showStatEnhancement();
}

function showItemPurchase() {
    if (window.player) player.showItemPurchase();
}

function backToMainMenu() {
    if (window.player) player.backToMainMenu();
}

function purchaseItem(itemId) {
    if (window.player) player.purchaseItem(itemId);
}

function showRestService() {
    if (window.player) player.showRestService();
}

function restService() {
    if (window.player) player.restService();
}

// Skill selection system wrappers
function showSkillSelection() {
    if (window.combatSystem) combatSystem.showSkillSelection();
}

function selectSkill(skillId) {
    if (window.combatSystem) combatSystem.selectSkill(skillId);
}

function cancelSkillSelection() {
    if (window.combatSystem) combatSystem.cancelSkillSelection();
}

// Target selection system wrappers (for First Aid)
function selectTarget(target) {
    if (window.combatSystem) combatSystem.selectTarget(target);
}

function cancelTargetSelection() {
    if (window.combatSystem) combatSystem.cancelTargetSelection();
}

// Global test functions for debugging
function testFloorProgression(floor = 2) {
    if (window.floorProgressionNotification) {
        window.floorProgressionNotification.testNotification(floor);
    }
}

function testTrapPit() {
    if (window.trapPitNotification) {
        const testMessages = [
            'Alice fell into a trap pit and took 15 damage!',
            'Bob fell into a trap pit and took 12 damage!'
        ];
        window.trapPitNotification.showNotification(testMessages);
    } else {
        console.error('Trap pit notification system not available');
    }
}

function testWildMeat() {
    console.log('Testing wild meat notification...');
    console.log('wildMeatNotification available:', typeof wildMeatNotification !== 'undefined');
    if (typeof wildMeatNotification !== 'undefined') {
        wildMeatNotification.showNotification();
    } else {
        console.error('Wild meat notification system not available');
    }
}

// Test animation functionality
function testAnimations() {
    console.log('Testing animation functionality...');

    // Test CSS animations
    const testElement = document.createElement('div');
    testElement.className = 'title-text';
    testElement.textContent = 'Animation Test';
    testElement.style.position = 'fixed';
    testElement.style.top = '50px';
    testElement.style.left = '50px';
    testElement.style.zIndex = '10000';
    document.body.appendChild(testElement);

    setTimeout(() => {
        document.body.removeChild(testElement);
        console.log('CSS animation test completed');
    }, 3000);

    // Test JavaScript animations
    if (renderer) {
        console.log('Testing JavaScript animations in renderer...');
        renderer.render();
        console.log('Renderer animation test completed');
    }

    // Test game loop
    if (gameCore && gameCore.gameLoopId) {
        console.log('Game loop is running:', !!gameCore.gameLoopId);
    } else {
        console.log('Game loop is not running');
    }

    console.log('Animation tests completed');
}
// Note: Game initialization is handled by the script in index.html
