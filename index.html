<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retro 3D Dungeon Explorer RPG</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="main-view">
            <canvas id="gameCanvas" width="800" height="600"></canvas>
            <div class="floor-info">
                <div>Floor: <span id="currentFloor">1</span>/10</div>
                <div>Depth: <span id="dungeonDepth">Shallow</span></div>
                <div>Position: (<span id="playerX">10</span>, <span id="playerY">10</span>)</div>
            </div>
            
            <div class="minimap">
                <h4>Dungeon Map</h4>
                <div class="minimap-grid" id="minimapGrid"></div>
            </div>
            
            <div class="gold-display">
                <div class="gold-icon">💰</div>
                <div class="gold-amount">
                    <span id="partyGold">0</span> Gold
                </div>
            </div>
        </div>
        
        <div class="ui-panel">
            <div class="game-title">RETRO DUNGEON EXPLORER</div>
            
            <div class="party-info">
                <h3>Party Status</h3>
                <div id="partyMembers"></div>
            </div>
            
            <div class="game-info">
                <div>Turn: <span id="turnCounter">0</span></div>
                <div>Enemies Defeated: <span id="enemiesDefeated">0</span></div>
                <div>Steps Taken: <span id="stepsTaken">0</span></div>
                <div>Enemy Respawn: <span id="respawnTimer"></span> steps</div>
            </div>
            
            <div class="controls">
                <h3>Action</h3>
                <button class="control-btn" onclick="useItem()">USE ITEM</button>
                <button class="control-btn" onclick="descendStairs()" id="descendBtn" style="display:none;">Descend Stairs</button>
                <button class="control-btn" onclick="openDoor()" id="doorBtn" style="display:none;">Open Door</button>
                <button class="control-btn" onclick="useHealPoint()" id="healBtn" style="display:none;">Use Healing Point</button>
            </div>
        </div>
    </div>
    
    <div class="combat-ui" id="combatUI">
        <div class="combat-layout">
            <div class="enemy-area-left" id="enemyAreaLeft">
                <!-- Single enemy or first enemy of multiple will be displayed here -->
            </div>
            
            <div class="combat-center">
                <div class="combat-party-info" id="combatPartyInfo">
                    <h4>Party Status</h4>
                    <div class="combat-party-members" id="combatPartyMembers">
                        <!-- Party member cards will be dynamically generated here -->
                    </div>
                </div>
                
                <div class="current-turn-info" id="currentTurnInfo">
                    <div class="turn-indicator">
                        <span class="turn-label">Current Turn:</span>
                        <span class="turn-member" id="currentTurnMember">Warrior</span>
                    </div>
                    <div class="turn-member-stats" id="turnMemberStats">
                        LIFE: 100/100 | STR: 23 | INT: 10 | AGL: 16
                    </div>
                </div>
                
                <div class="combat-actions">
                    <button class="action-btn" onclick="playerAction('attack')">ATTACK</button>
                    <button class="action-btn" onclick="playerAction('magic')">MAGIC</button>
                    <button class="action-btn" onclick="playerAction('defend')">DEFEND</button>
                    <button class="action-btn" onclick="playerAction('flee')">FLEE</button>
                    <button class="action-btn" onclick="playerAction('concentrate')">CONCENTRATE</button>
                    <button class="action-btn" onclick="playerAction('skill')">SKILL</button>
                </div>
                
                <div class="combat-log" id="combatLog"></div>
            </div>

            <div class="enemy-area-right" id="enemyAreaRight">
                <!-- Second enemy of multiple enemies will be displayed here -->
            </div>
        </div>

        <!-- Skill Selection Menu -->
        <div class="skill-selection-ui" id="skillSelectionUI" style="display: none;">
            <div class="skill-selection-content">
                <div class="skill-selection-header">
                    <h3>Select Skill</h3>
                    <p id="skillSelectionDescription">Choose a skill to use:</p>
                </div>
                <div class="skill-selection-list" id="skillSelectionList">
                    <!-- Skill buttons will be generated here -->
                </div>
                <div class="skill-selection-buttons">
                    <button class="action-btn" onclick="cancelSkillSelection()">Cancel</button>
                </div>
            </div>
        </div>

        <!-- Target Selection Menu for First Aid -->
        <div class="target-selection-ui" id="targetSelectionUI" style="display: none;">
            <div class="target-selection-content">
                <div class="target-selection-header">
                    <h3>Select Target</h3>
                    <p id="targetSelectionDescription">Choose a party member to heal:</p>
                </div>
                <div class="target-selection-list" id="targetSelectionList">
                    <!-- Target buttons will be generated here -->
                </div>
                <div class="target-selection-buttons">
                    <button class="action-btn" onclick="cancelTargetSelection()">Cancel</button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="door-ui" id="doorUI">
        <h3>You found a door!</h3>
        <p>Do you want to open it?</p>
        <button class="action-btn" onclick="confirmOpenDoor()">Open</button>
        <button class="action-btn" onclick="closeDoorUI()">Cancel</button>
    </div>
    
    <div class="shop-ui" id="shopUI">
        <div class="shop-content">
            <div class="shop-header">
                <h2>What is thy wish?</h2>
                <div class="shop-gold-display">
                    <span class="gold-label">Gold:</span>
                    <span id="shopGoldAmount" class="gold-amount">0</span>
                </div>
            </div>
            <div class="shop-main">
                <div class="shop-image-container">
                    <img id="shopImage" src="" alt="Shop" class="shop-image">
                </div>
                <div class="shop-details">
                    <h3 id="shopName">Shop</h3>
                    
                    <!-- Initial shop menu -->
                    <div id="shopMainMenu" class="shop-menu">
                        <div class="shop-option-buttons">
                            <button class="shop-option-btn" onclick="showStatEnhancement()">
                                <div class="option-title">Ability Enhancement</div>
                                <div class="option-description">Improve character stats</div>
                            </button>
                            <button class="shop-option-btn" onclick="showItemPurchase()">
                                <div class="option-title">Buy Items</div>
                                <div class="option-description">Purchase consumable items</div>
                            </button>
                            <button class="shop-option-btn" onclick="showRestService()">
                                <div class="option-title">Rest & Healing</div>
                                <div class="option-description">Restore party health</div>
                            </button>
                        </div>
                        <button class="action-btn" onclick="closeShopUI()">Leave</button>
                    </div>
                    
                    <!-- Stat enhancement menu -->
                    <div id="shopStatMenu" class="shop-menu" style="display: none;">
                        <div class="shop-item-info">
                            <p>Item: <span id="shopItem">Item</span></p>
                            <p>Cost: <span id="shopCost">0</span> Gold</p>
                        </div>
                        <div class="shop-buttons">
                            <button class="action-btn" onclick="initiatePurchase()">Purchase</button>
                            <button class="action-btn" onclick="backToMainMenu()">Back</button>
                        </div>
                    </div>
                    
                    <!-- Item purchase menu -->
                    <div id="shopItemMenu" class="shop-menu" style="display: none;">
                        <div class="shop-item-list">
                            <div class="shop-item-option" onclick="purchaseItem('health_potion')">
                                <div class="item-option-name">Health Potion</div>
                                <div class="item-option-description">Restores 20% of max life</div>
                                <div class="item-option-price">500 Gold</div>
                            </div>
                            <div class="shop-item-option" onclick="purchaseItem('smoke_screen')">
                                <div class="item-option-name">Smoke Screen</div>
                                <div class="item-option-description">Avoid enemy encounters for 10 steps</div>
                                <div class="item-option-price">600 Gold</div>
                            </div>
                            <div class="shop-item-option" onclick="purchaseItem('antidote')">
                                <div class="item-option-name">Antidote</div>
                                <div class="item-option-description">Cures poison status effect</div>
                                <div class="item-option-price">300 Gold</div>
                            </div>
                            <div class="shop-item-option" onclick="purchaseItem('mapping_tool')">
                                <div class="item-option-name">Mapping Tool</div>
                                <div class="item-option-description">Reveals the dungeon map and enables minimap</div>
                                <div class="item-option-price">1500 Gold</div>
                            </div>
                        </div>
                        <button class="action-btn" onclick="backToMainMenu()">Back</button>
                    </div>

                    <!-- Rest service menu -->
                    <div id="shopRestMenu" class="shop-menu" style="display: none;">
                        <div class="shop-item-info">
                            <p>Service: Rest & Healing</p>
                            <p>Effect: Fully restore all party members' health</p>
                            <p>Cost: <span id="restCost">0</span> Gold</p>
                        </div>
                        <div class="shop-buttons">
                            <button class="action-btn" onclick="restService()">Rest</button>
                            <button class="action-btn" onclick="backToMainMenu()">Back</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="item-ui" id="itemUI">
        <div class="item-content">
            <div class="item-header">
                <h3>Select Item</h3>
                <p>Choose an item to use:</p>
            </div>
            <div class="item-list" id="itemList">
                <!-- Item buttons will be generated here -->
            </div>
            <button class="action-btn" onclick="closeItemUI()">Cancel</button>
        </div>
    </div>
    
    <div class="party-selection-ui" id="partySelectionUI">
        <div class="party-selection-content">
            <div class="party-selection-header">
                <h3>Choose a party member</h3>
                <p id="selectionDescription">Select who should receive the enhancement:</p>
            </div>
            <div class="party-selection-members" id="partySelectionMembers">
                <!-- Party member buttons will be generated here -->
            </div>
            <button class="action-btn" onclick="cancelPartySelection()">Cancel</button>
        </div>
    </div>
    
    <div class="level-up-modal" id="levelUpModal">
        <div class="level-up-content">
            <div class="level-up-title">LEVEL UP!</div>
            <div id="levelUpParty"></div>
            <button class="action-btn" onclick="closeLevelUp()">Continue</button>
        </div>
    </div>
    
    <div class="game-over" id="gameOver">
        <div class="game-over-content">
            <h2>GAME OVER</h2>
            <p>Your party has been defeated...</p>
            <button class="action-btn" onclick="location.reload()">Restart</button>
        </div>
    </div>
    
    <div class="victory-screen" id="victoryScreen">
        <div class="victory-content">
            <h2>VICTORY!</h2>
            <p>You have conquered the dungeon!</p>
            <p>The ancient evil has been defeated!</p>
            <button class="action-btn" onclick="location.reload()">Play Again</button>
        </div>
    </div>

    <div class="mapping-tool-modal" id="mappingToolModal">
        <div class="mapping-tool-content">
            <div class="mapping-tool-title">MAPPING TOOL ACQUIRED!</div>
            <img id="mappingToolImage" src="assets/images/MagicalMappingTool.png" alt="Magical Mapping Tool" class="mapping-tool-image">
            <div class="mapping-tool-description">
                You have obtained the Magical Mapping Tool!<br>
                The dungeon map is now available and will reveal explored areas.
            </div>
            <div class="mapping-tool-dismiss">Click anywhere to continue...</div>
        </div>
    </div>

    <div class="health-potion-modal" id="healthPotionModal">
        <div class="health-potion-content">
            <div class="health-potion-title">HEALTH POTION ACQUIRED!</div>
            <img id="healthPotionImage" src="assets/images/heal potion.png" alt="Health Potion" class="health-potion-image">
            <div class="health-potion-description">
                You have obtained a Health Potion!<br>
                It will restore 20% of your maximum life when used.
            </div>
            <div class="health-potion-dismiss">Click anywhere to continue...</div>
        </div>
    </div>

    <div class="smoke-screen-modal" id="smokeScreenModal">
        <div class="smoke-screen-content">
            <div class="smoke-screen-title">SMOKE SCREEN ACQUIRED!</div>
            <img id="smokeScreenImage" src="assets/images/smoke screen.png" alt="Smoke Screen" class="smoke-screen-image">
            <div class="smoke-screen-description">
                You have obtained a Smoke Screen!<br>
                It will help you avoid enemy encounters for 10 steps.
            </div>
            <div class="smoke-screen-dismiss">Click anywhere to continue...</div>
        </div>
    </div>

    <div class="antidote-modal" id="antidoteModal">
        <div class="antidote-content">
            <div class="antidote-title">ANTIDOTE ACQUIRED!</div>
            <img id="antidoteImage" src="assets/images/antidote.png" alt="Antidote" class="antidote-image">
            <div class="antidote-description">
                You have obtained an Antidote!<br>
                It will cure poison status effects when used.
            </div>
            <div class="antidote-dismiss">Click anywhere to continue...</div>
        </div>
    </div>

    <div class="healing-point-modal" id="healingPointModal">
        <div class="healing-point-content">
            <div class="healing-point-title">HEALING POINT USED!</div>
            <img id="healingPointImage" src="assets/images/HealingPoint.png" alt="Healing Point" class="healing-point-image">
            <div class="healing-point-description">
                All party members have been fully healed!<br>
                Your party is restored to maximum health.
            </div>
            <div class="healing-point-dismiss">Click anywhere to continue...</div>
        </div>
    </div>

    <div class="orb-modal" id="orbModal">
        <div class="orb-content">
            <div class="orb-title">ORB ACQUIRED!</div>
            <img id="orbImage" src="assets/images/orb.png" alt="Orb" class="orb-image">
            <div class="orb-description">
                You have obtained a mysterious Orb!<br>
                Its power remains unknown...
            </div>
            <div class="orb-dismiss">Click anywhere to continue...</div>
        </div>
    </div>

    <div class="mystic-orb-modal" id="mysticOrbModal">
        <div class="mystic-orb-content">
            <div class="mystic-orb-title">MYSTIC ORB ACQUIRED!</div>
            <img id="mysticOrbImage" src="assets/images/orb special.png" alt="Mystic Orb" class="mystic-orb-image">
            <div class="mystic-orb-description">
                You have obtained a Mystic Orb!<br>
                It pulses with otherworldly energy...
            </div>
            <div class="mystic-orb-dismiss">Click anywhere to continue...</div>
        </div>
    </div>

    <div class="floor-progression-modal" id="floorProgressionModal">
        <div class="floor-progression-content">
            <div class="floor-progression-title">ENTERING FLOOR 2</div>
            <img id="floorProgressionImage" src="assets/images/dungeon.png" alt="Dungeon" class="floor-progression-image">
            <div class="floor-progression-description">
                You descend deeper into the dungeon...<br>
                The darkness grows thicker around you.
            </div>
            <div class="floor-progression-dismiss">Click anywhere to continue...</div>
        </div>
    </div>

    <div class="trap-pit-modal" id="trapPitModal">
        <div class="trap-pit-content">
            <div class="trap-pit-title">⚠️ TRAP PIT! ⚠️</div>
            <img id="trapPitImage" src="assets/images/pitfall.png" alt="Trap Pit" class="trap-pit-image">
            <div class="trap-pit-messages">
                <!-- Damage messages will be inserted here dynamically -->
            </div>
            <div class="trap-pit-dismiss">Auto-dismissing in 3 seconds...</div>
        </div>
    </div>

    <div class="wild-meat-modal" id="wildMeatModal">
        <div class="wild-meat-content">
            <div class="wild-meat-title">WILD MEAT OBTAINED!</div>
            <img id="wildMeatImage" src="assets/images/wild meat.png" alt="Wild Meat" class="wild-meat-image">
            <div class="wild-meat-description">
                Fresh meat from the defeated beast!<br>
                Enhanced healing will be applied after combat.
            </div>
            <div class="wild-meat-dismiss">Click anywhere to continue...</div>
        </div>
    </div>

    <!-- Modular JavaScript files - loaded in dependency order -->
    <!-- Core modules -->
    <script src="js/core/Constants.js"></script>
    <script src="js/core/GameState.js"></script>
    
    <!-- Utility modules -->
    <script src="js/utils/ImageManager.js"></script>

    <!-- System modules -->
    <script src="js/systems/Dungeon.js"></script>
    <script src="js/systems/Player.js"></script>
    <script src="js/systems/Combat.js"></script>
    <script src="js/systems/Character.js"></script>
    <script src="js/systems/Renderer.js"></script>
    <script src="js/systems/SoundManager.js"></script>

    <!-- UI modules -->
    <script src="js/ui/UIManager.js"></script>
    <script src="js/ui/MappingToolNotification.js"></script>
    <script src="js/ui/HealthPotionNotification.js"></script>
    <script src="js/ui/SmokeScreenNotification.js"></script>
    <script src="js/ui/AntidoteNotification.js"></script>
    <script src="js/ui/HealingPointNotification.js"></script>
    <script src="js/ui/OrbNotification.js"></script>
    <script src="js/ui/MysticOrbNotification.js"></script>
    <script src="js/ui/FloorProgressionNotification.js"></script>
    <script src="js/ui/TrapPitNotification.js"></script>
    <script src="js/ui/WildMeatNotification.js"></script>
    <script src="js/ui/TitleScreen.js"></script>
    <script src="js/ui/OptionScreen.js"></script>
    <script src="js/ui/InputHandler.js"></script>
    
    <!-- Game core and initialization -->
    <script src="js/core/GameCore.js"></script>
    <script src="js/main.js"></script>

    <!-- Silent initialization script -->
    <script>
        (function() {
            'use strict';

            function showError(message) {
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: #ff0000;
                    color: white;
                    padding: 20px;
                    border-radius: 10px;
                    z-index: 9999;
                    font-family: 'Orbitron', monospace;
                    text-align: center;
                `;
                errorDiv.innerHTML = `
                    <h2>Game Loading Error</h2>
                    <p>${message}</p>
                    <p>Please refresh the page and try again.</p>
                    <button onclick="location.reload()" style="padding: 10px; margin-top: 10px;">Reload Page</button>
                `;
                document.body.appendChild(errorDiv);
            }

            // Check if all required modules are loaded
            function checkDependencies() {
                const requiredModules = [
                    'CONSTANTS', 'gameState', 'player', 'dungeonGenerator',
                    'combatSystem', 'characterSystem', 'renderer',
                    'uiManager', 'inputHandler', 'gameCore', 'titleScreen', 'optionScreen'
                ];

                const missing = [];
                const loaded = [];
                for (const module of requiredModules) {
                    if (typeof window[module] === 'undefined') {
                        missing.push(module);
                    } else {
                        loaded.push(module);
                    }
                }

                console.log('Loaded modules:', loaded);
                console.log('Missing modules:', missing);

                if (missing.length > 0) {
                    showError(`Missing required modules: ${missing.join(', ')}`);
                    return false;
                }

                return true;
            }

            // Initialize the game silently
            async function initializeGame() {
                try {
                    console.log('Starting game initialization...');

                    // Check dependencies
                    console.log('Checking dependencies...');
                    if (!checkDependencies()) {
                        console.error('Dependencies check failed');
                        return;
                    }

                    console.log('Dependencies check passed');

                    // Initialize the game
                    console.log('Calling gameCore.init()...');
                    await gameCore.init();
                    console.log('gameCore.init() completed');

                } catch (error) {
                    console.error('Game initialization failed:', error);
                    showError(`Initialization failed: ${error.message}`);
                }
            }

            // Global error handler
            window.addEventListener('error', function(event) {
                console.error('Global error:', event.error);
                showError(`Script error: ${event.error?.message || 'Unknown error'}`);
            });

            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeGame);
            } else {
                // DOM is already ready
                setTimeout(initializeGame, 100);
            }

        })();
    </script>
</body>
</html>
