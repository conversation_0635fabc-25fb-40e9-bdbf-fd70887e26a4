// ===== IMAGE MANAGER =====
// Handles image loading, validation, and fallback mechanisms

class ImageManager {
    constructor() {
        this.imageCache = new Map();
        this.loadingPromises = new Map();
        this.failedImages = new Set();
    }

    // Preload an image and return a promise
    preloadImage(src) {
        // Return cached result if available
        if (this.imageCache.has(src)) {
            return Promise.resolve(this.imageCache.get(src));
        }

        // Return existing loading promise if in progress
        if (this.loadingPromises.has(src)) {
            return this.loadingPromises.get(src);
        }

        // Create new loading promise
        const loadingPromise = new Promise((resolve, reject) => {
            const img = new Image();
            
            img.onload = () => {
                this.imageCache.set(src, { success: true, element: img });
                this.loadingPromises.delete(src);
                resolve({ success: true, element: img });
            };
            
            img.onerror = () => {
                this.failedImages.add(src);
                this.imageCache.set(src, { success: false, error: 'Failed to load' });
                this.loadingPromises.delete(src);
                console.warn(`Failed to load image: ${src}`);
                resolve({ success: false, error: 'Failed to load' });
            };
            
            img.src = src;
        });

        this.loadingPromises.set(src, loadingPromise);
        return loadingPromise;
    }

    // Preload all monster images
    async preloadMonsterImages() {
        const monsterImages = [];
        
        // Collect all monster image paths
        Object.values(CONSTANTS.MONSTER_TYPES).forEach(monster => {
            if (monster.imagePath) {
                monsterImages.push(monster.imagePath);
            }
        });

        Object.values(CONSTANTS.BOSS_TYPES).forEach(boss => {
            if (boss.imagePath) {
                monsterImages.push(boss.imagePath);
            }
        });

        // Preload all images
        const results = await Promise.all(
            monsterImages.map(path => this.preloadImage(path))
        );

        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;

        console.log(`Monster images preloaded: ${successful} successful, ${failed} failed`);
        return { successful, failed, total: results.length };
    }

    // Preload all shop images
    async preloadShopImages() {
        const shopImages = [];
        
        // Collect all shop image paths
        Object.values(CONSTANTS.SHOPS).forEach(shop => {
            if (shop.image) {
                shopImages.push(shop.image);
            }
        });

        // Preload all images
        const results = await Promise.all(
            shopImages.map(path => this.preloadImage(path))
        );

        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;

        console.log(`Shop images preloaded: ${successful} successful, ${failed} failed`);
        return { successful, failed, total: results.length };
    }

    // Preload UI images (mapping tool, trap pit, wild meat, title screen, combat background, etc.)
    async preloadUIImages() {
        const uiImages = [
            'assets/images/MagicalMappingTool.png',
            'assets/images/pitfall.png',
            'assets/images/wild meat.png',
            'assets/images/title.png',
            'assets/images/combat.png'
        ];

        // Preload all images
        const results = await Promise.all(
            uiImages.map(path => this.preloadImage(path))
        );

        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;

        console.log(`UI images preloaded: ${successful} successful, ${failed} failed`);
        return { successful, failed, total: results.length };
    }

    // Preload all game images
    async preloadAllImages() {
        const [monsterResults, shopResults, uiResults] = await Promise.all([
            this.preloadMonsterImages(),
            this.preloadShopImages(),
            this.preloadUIImages()
        ]);

        const totalSuccessful = monsterResults.successful + shopResults.successful + uiResults.successful;
        const totalFailed = monsterResults.failed + shopResults.failed + uiResults.failed;
        const totalImages = monsterResults.total + shopResults.total + uiResults.total;

        console.log(`All images preloaded: ${totalSuccessful}/${totalImages} successful`);
        
        if (totalFailed > 0) {
            console.warn(`${totalFailed} images failed to load. Fallback mechanisms will be used.`);
        }

        return {
            successful: totalSuccessful,
            failed: totalFailed,
            total: totalImages,
            monsters: monsterResults,
            shops: shopResults,
            ui: uiResults
        };
    }

    // Check if an image is available
    isImageAvailable(src) {
        const cached = this.imageCache.get(src);
        return cached && cached.success;
    }

    // Get image element if available
    getImageElement(src) {
        const cached = this.imageCache.get(src);
        return cached && cached.success ? cached.element : null;
    }

    // Create an image element with fallback
    createImageWithFallback(src, alt, className, fallbackContent, fallbackClassName) {
        const container = document.createElement('div');
        container.className = 'image-container';

        if (this.isImageAvailable(src)) {
            // Image is available, use it
            const img = document.createElement('img');
            img.src = src;
            img.alt = alt;
            img.className = className;
            container.appendChild(img);
        } else {
            // Use fallback
            const fallback = document.createElement('div');
            fallback.className = fallbackClassName;
            fallback.innerHTML = fallbackContent;
            container.appendChild(fallback);
        }

        return container;
    }

    // Get loading statistics
    getStats() {
        const total = this.imageCache.size;
        const successful = Array.from(this.imageCache.values()).filter(v => v.success).length;
        const failed = total - successful;

        return {
            total,
            successful,
            failed,
            failedPaths: Array.from(this.failedImages)
        };
    }

    // Generate diagnostic report
    generateDiagnosticReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: this.getStats(),
            details: {
                monsters: {},
                shops: {},
                bosses: {}
            }
        };

        // Check monster images
        Object.entries(CONSTANTS.MONSTER_TYPES).forEach(([key, monster]) => {
            if (monster.imagePath) {
                const cached = this.imageCache.get(monster.imagePath);
                report.details.monsters[key] = {
                    name: monster.name,
                    path: monster.imagePath,
                    status: cached ? (cached.success ? 'loaded' : 'failed') : 'not_checked',
                    available: this.isImageAvailable(monster.imagePath)
                };
            }
        });

        // Check boss images
        Object.entries(CONSTANTS.BOSS_TYPES).forEach(([key, boss]) => {
            if (boss.imagePath) {
                const cached = this.imageCache.get(boss.imagePath);
                report.details.bosses[key] = {
                    name: boss.name,
                    path: boss.imagePath,
                    status: cached ? (cached.success ? 'loaded' : 'failed') : 'not_checked',
                    available: this.isImageAvailable(boss.imagePath)
                };
            }
        });

        // Check shop images
        Object.entries(CONSTANTS.SHOPS).forEach(([key, shop]) => {
            if (shop.image) {
                const cached = this.imageCache.get(shop.image);
                report.details.shops[key] = {
                    name: shop.name,
                    path: shop.image,
                    status: cached ? (cached.success ? 'loaded' : 'failed') : 'not_checked',
                    available: this.isImageAvailable(shop.image)
                };
            }
        });

        return report;
    }

    // Print diagnostic report to console
    printDiagnosticReport() {
        const report = this.generateDiagnosticReport();

        console.group('🖼️ Image Manager Diagnostic Report');
        console.log(`Generated: ${report.timestamp}`);
        console.log(`Total Images: ${report.summary.total}`);
        console.log(`✅ Successful: ${report.summary.successful}`);
        console.log(`❌ Failed: ${report.summary.failed}`);

        if (report.summary.failed > 0) {
            console.group('❌ Failed Images:');
            report.summary.failedPaths.forEach(path => {
                console.log(`  • ${path}`);
            });
            console.groupEnd();
        }

        console.group('📊 Detailed Status:');

        console.group('👹 Monsters:');
        Object.entries(report.details.monsters).forEach(([key, info]) => {
            const icon = info.status === 'loaded' ? '✅' : info.status === 'failed' ? '❌' : '⏳';
            console.log(`  ${icon} ${info.name}: ${info.path}`);
        });
        console.groupEnd();

        console.group('🏪 Shops:');
        Object.entries(report.details.shops).forEach(([key, info]) => {
            const icon = info.status === 'loaded' ? '✅' : info.status === 'failed' ? '❌' : '⏳';
            console.log(`  ${icon} ${info.name}: ${info.path}`);
        });
        console.groupEnd();

        console.group('👑 Bosses:');
        Object.entries(report.details.bosses).forEach(([key, info]) => {
            const icon = info.status === 'loaded' ? '✅' : info.status === 'failed' ? '❌' : '⏳';
            console.log(`  ${icon} ${info.name}: ${info.path}`);
        });
        console.groupEnd();

        console.groupEnd();
        console.groupEnd();

        return report;
    }
}

// Create global instance
const imageManager = new ImageManager();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.imageManager = imageManager;
}
