// ===== CHARACTER SYSTEM =====
// Party management, experience, leveling, and character progression

class Character {
    constructor() {
        // Character system will work with gameState.party
    }
    
    // Create the initial party
    createParty() {
        const classes = Object.values(CONSTANTS.CLASSES);
        const party = [];
        
        classes.forEach(characterClass => {
            party.push({
                name: characterClass.name,
                level: 1,
                exp: 0,
                expToNext: 100,
                maxLife: characterClass.life,
                life: characterClass.life,
                str: characterClass.str,
                int: characterClass.int,
                agl: characterClass.agl,
                defending: false,
                concentrated: false,
                damageBoost: false,
                skills: [],
                // Skill states
                shieldActive: false,        // WARRIOR SHIELD effect
                shieldTurnsLeft: 0,
                // Status effects
                poisoned: false,            // Poison status effect
                poisonTurnsLeft: 0,         // Turns remaining for poison
                paralyzed: false,           // Paralysis status effect
                paralysisTurnsLeft: 0,      // Turns remaining for paralysis
                silenced: false,            // Silence status effect (for enemies)
                // Level 20 skill effects
                protected: false,           // PROTECT effect (defense doubled)
                protectedTurnsLeft: 0,      // Turns remaining for protection
                swiftMovement: false,       // SWIFT MOVEMENT effect (agility doubled)
                swiftMovementTurnsLeft: 0,  // Turns remaining for swift movement
                silenceTurnsLeft: 0,        // Turns remaining for silence
                hidden: false,              // Hidden status effect (for Rangers)
                hiddenTurnsLeft: 0          // Turns remaining for hidden
            });
        });
        
        gameState.setParty(party);
        return party;
    }
    
    // Award experience to the party
    awardExp(exp) {
        const levelUpMembers = [];
        
        gameState.party.forEach(member => {
            if (member.life > 0) {
                member.exp += Math.floor(exp / gameState.party.length);
                const levelUpData = this.checkLevelUp(member);
                if (levelUpData) {
                    levelUpMembers.push(levelUpData);
                }
            }
        });
        
        // Show level up modal if any members leveled up
        if (levelUpMembers.length > 0) {
            this.showPartyLevelUp(levelUpMembers);
        }
        
        gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    // Check if a character should level up
    checkLevelUp(member) {
        if (member.exp >= member.expToNext) {
            member.level++;
            member.exp -= member.expToNext;
            member.expToNext = Math.floor(member.expToNext * 1.3);
            
            // Calculate stat increases based on class
            const increases = this.calculateStatIncreases(member.name);
            
            // Apply stat increases
            member.maxLife += increases.life;
            member.life += increases.life;
            member.str += increases.str;
            member.int += increases.int;
            member.agl += increases.agl;
            
            // Check for skill learning
            const newSkill = this.checkSkillLearning(member);
            
            return {
                member: member,
                increases: increases,
                newSkill: newSkill
            };
        }
        return null;
    }
    
    // Calculate stat increases based on character class
    calculateStatIncreases(className) {
        const growthRates = CONSTANTS.GROWTH_RATES[className.toUpperCase()] || CONSTANTS.GROWTH_RATES.WARRIOR;
        
        return {
            life: this.randomBetween(growthRates.life.min, growthRates.life.max),
            str: this.randomBetween(growthRates.str.min, growthRates.str.max),
            int: this.randomBetween(growthRates.int.min, growthRates.int.max),
            agl: this.randomBetween(growthRates.agl.min, growthRates.agl.max)
        };
    }
    
    // Generate random number between min and max (inclusive)
    randomBetween(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    
    // Show level up modal for multiple party members
    showPartyLevelUp(levelUpMembers) {
        const modal = document.getElementById('levelUpModal');
        const partyDiv = document.getElementById('levelUpParty');
        
        partyDiv.innerHTML = '';
        
        levelUpMembers.forEach(levelUpData => {
            const member = levelUpData.member;
            const increases = levelUpData.increases;
            const newSkill = levelUpData.newSkill;
            
            const memberDiv = document.createElement('div');
            memberDiv.className = 'level-up-member';
            
            let skillHtml = '';
            if (newSkill) {
                skillHtml = `<div class="skill-learned">✨ Learned: ${newSkill.name}!</div>`;
            }
            
            memberDiv.innerHTML = `
                <div class="level-up-member-name">${member.name} reached Level ${member.level}!</div>
                <div class="level-up-stats">
                    <div class="stat-increase">LIFE +${increases.life}</div>
                    <div class="stat-increase">STR +${increases.str}</div>
                    <div class="stat-increase">INT +${increases.int}</div>
                    <div class="stat-increase">AGL +${increases.agl}</div>
                </div>
                ${skillHtml}
            `;
            
            partyDiv.appendChild(memberDiv);
        });
        
        modal.classList.add('active');
        gameState.dispatchEvent(CONSTANTS.EVENTS.LEVEL_UP, { members: levelUpMembers });
    }
    
    // Close level up modal
    closeLevelUp() {
        document.getElementById('levelUpModal').classList.remove('active');
        gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    // Get character by name
    getCharacterByName(name) {
        return gameState.party.find(member => member.name === name);
    }
    
    // Heal all party members
    healParty() {
        gameState.party.forEach(member => {
            member.life = member.maxLife;
        });
        gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    // Heal specific character
    healCharacter(character, amount) {
        character.life = Math.min(character.maxLife, character.life + amount);
        gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    // Damage specific character
    damageCharacter(character, amount) {
        character.life = Math.max(0, character.life - amount);
        gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    // Check if character is alive
    isAlive(character) {
        return character.life > 0;
    }
    
    // Get alive party members
    getAliveMembers() {
        return gameState.party.filter(member => this.isAlive(member));
    }
    
    // Get dead party members
    getDeadMembers() {
        return gameState.party.filter(member => !this.isAlive(member));
    }
    
    // Revive character with specified life percentage
    reviveCharacter(character, lifePercentage = 0.5) {
        if (!this.isAlive(character)) {
            character.life = Math.floor(character.maxLife * lifePercentage);
            gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
        }
    }
    
    // Get party statistics
    getPartyStats() {
        const alive = this.getAliveMembers();
        const totalLevel = gameState.party.reduce((sum, member) => sum + member.level, 0);
        const averageLevel = totalLevel / gameState.party.length;
        
        return {
            totalMembers: gameState.party.length,
            aliveMembers: alive.length,
            deadMembers: gameState.party.length - alive.length,
            totalLevel: totalLevel,
            averageLevel: Math.floor(averageLevel),
            totalExp: gameState.party.reduce((sum, member) => sum + member.exp, 0)
        };
    }
    
    // Reset defending status for all party members
    resetDefendingStatus() {
        gameState.party.forEach(member => {
            member.defending = false;
        });
    }

    // Check if character learns a new skill at this level
    checkSkillLearning(member) {
        if (CONSTANTS.SKILLS.LEARNING_LEVELS.includes(member.level)) {
            const className = member.name.toUpperCase();
            const skillSlots = CONSTANTS.SKILLS.SKILL_SLOTS[className];
            
            if (skillSlots) {
                const skillToLearn = skillSlots.find(skill => skill.level === member.level);
                if (skillToLearn) {
                    member.skills.push({
                        name: skillToLearn.name,
                        description: skillToLearn.description,
                        level: skillToLearn.level,
                        skillId: skillToLearn.skillId  // Include skillId for proper skill execution
                    });
                    return skillToLearn;
                }
            }
        }
        return null;
    }

    // Check if character can use skills
    canUseSkills(member) {
        return member.skills.length > 0 &&
               !this.isCharacterParalyzed(member) &&
               !this.isIncapacitated(member) &&
               member.life > 0;
    }

    // Reset concentration and damage boost for all party members
    resetCombatStates() {
        gameState.party.forEach(member => {
            member.concentrated = false;
            member.damageBoost = false;
            member.defending = false;
        });
    }

    // Set concentrate state for a character
    setConcentrated(member, state) {
        member.concentrated = state;
        if (state) {
            // Clear damage boost when concentrating
            member.damageBoost = false;
        }
    }

    // Set damage boost state for a character
    setDamageBoost(member, state) {
        member.damageBoost = state;
        if (state) {
            // Clear concentration when getting damage boost
            member.concentrated = false;
        }
    }

    // ===== SKILL MANAGEMENT FUNCTIONS =====

    // Activate SHIELD skill for WARRIOR
    activateShield(warrior) {
        // Deactivate any existing shield first
        this.deactivateAllShields();

        warrior.shieldActive = true;
        warrior.shieldTurnsLeft = 2; // Lasts for 2 of the Warrior's turns

        return {
            success: true,
            message: `🛡️ ${warrior.name} activates SHIELD! All enemy attacks will target ${warrior.name} with reduced damage for 2 turns! 🛡️`
        };
    }

    // Deactivate all shields
    deactivateAllShields() {
        gameState.party.forEach(member => {
            member.shieldActive = false;
            member.shieldTurnsLeft = 0;
        });
    }

    // Get the current shield bearer (if any)
    getShieldBearer() {
        return gameState.party.find(member => member.shieldActive && member.life > 0);
    }

    // Activate EXORCISE skill for MAGE
    activateExorcise(mage, target) {
        if (!target) {
            return {
                success: false,
                message: `${mage.name} needs to select a target for EXORCISE!`
            };
        }

        // Check if target is an enemy
        if (!target.type) {
            return {
                success: false,
                message: `${mage.name} can only use EXORCISE on enemies!`
            };
        }

        // Calculate base magic damage
        const baseDamage = Math.floor(mage.int * (0.7 + Math.random() * 0.6));

        // Check if target has spiritual_body property for damage multiplier
        let finalDamage = baseDamage;
        let isSpiritual = false;

        // Check if enemy has spiritual_body property
        if (target.properties && target.properties.includes('spiritual_body')) {
            finalDamage = Math.floor(baseDamage * CONSTANTS.SKILLS.LEVEL_5_SKILLS.EXORCISE.spiritualDamageMultiplier);
            isSpiritual = true;
        }

        // Apply defending bonus (reduced effectiveness against magic)
        finalDamage = Math.max(1, finalDamage - (target.defending ? finalDamage * 0.3 : 0));

        // Apply damage
        target.life = Math.max(0, target.life - finalDamage);

        // Create appropriate message
        const effectivenessMessage = isSpiritual ?
            `💀 SUPER EFFECTIVE against spiritual enemy! 💀` :
            ``;

        return {
            success: true,
            message: `⚡ ${mage.name} casts EXORCISE for ${finalDamage} damage! ${effectivenessMessage} ⚡`,
            damage: finalDamage,
            isSpiritual: isSpiritual
        };
    }

    // Use FIRST AID skill for RANGER
    useFirstAid(ranger, target) {
        if (!target || target.life <= 0) {
            return {
                success: false,
                message: `${ranger.name} cannot use FIRST AID on an invalid target!`
            };
        }

        const healingAmount = Math.floor(target.maxLife * CONSTANTS.SKILLS.LEVEL_5_SKILLS.FIRST_AID.healingRate);
        const oldLife = target.life;
        target.life = Math.min(target.maxLife, target.life + healingAmount);
        const actualHealing = target.life - oldLife;

        // Play FIRST AID sound effect
        if (window.soundManager) {
            soundManager.playCombatSound('first_aid');
        }

        return {
            success: true,
            message: `💚 ${ranger.name} uses FIRST AID on ${target.name}! Recovered ${actualHealing} LIFE! 💚`,
            healingAmount: actualHealing
        };
    }

    // Update skill durations at end of turn
    updateSkillDurations(currentActor) {
        // Update shield duration (only for the shield bearer on their turn)
        if (currentActor.shieldActive && currentActor.shieldTurnsLeft > 0) {
            currentActor.shieldTurnsLeft--;
            if (currentActor.shieldTurnsLeft <= 0) {
                currentActor.shieldActive = false;
                return {
                    shieldExpired: true,
                    shieldBearer: currentActor.name
                };
            }
        }

        return {};
    }

    // Get available skills for a character
    getAvailableSkills(member) {
        if (!this.canUseSkills(member)) {
            return [];
        }

        // Return all learned skills for the character
        const className = member.name.toUpperCase();
        const availableSkills = [];

        // Add level 5 skills
        if (member.level >= 5) {
            switch (className) {
                case 'WARRIOR':
                    availableSkills.push({
                        ...CONSTANTS.SKILLS.LEVEL_5_SKILLS.SHIELD,
                        level: 5,
                        requiresTarget: false,
                        targetType: 'self'
                    });
                    break;
                case 'MAGE':
                    availableSkills.push({
                        ...CONSTANTS.SKILLS.LEVEL_5_SKILLS.EXORCISE,
                        level: 5,
                        requiresTarget: true,
                        targetType: 'enemy'
                    });
                    break;
                case 'RANGER':
                    availableSkills.push({
                        ...CONSTANTS.SKILLS.LEVEL_5_SKILLS.FIRST_AID,
                        level: 5,
                        requiresTarget: true,
                        targetType: 'ally'
                    });
                    break;
            }
        }

        // Add level 10 skills
        if (member.level >= 10) {
            switch (className) {
                case 'WARRIOR':
                    availableSkills.push({
                        ...CONSTANTS.SKILLS.LEVEL_10_SKILLS.TRIPLE_STRIKE,
                        level: 10,
                        requiresTarget: true,
                        targetType: 'enemy'
                    });
                    break;
                case 'MAGE':
                    availableSkills.push({
                        ...CONSTANTS.SKILLS.LEVEL_10_SKILLS.SILENCE,
                        level: 10,
                        requiresTarget: false,
                        targetType: 'all_enemies'
                    });
                    break;
                case 'RANGER':
                    availableSkills.push({
                        ...CONSTANTS.SKILLS.LEVEL_10_SKILLS.HIDE,
                        level: 10,
                        requiresTarget: false,
                        targetType: 'self'
                    });
                    break;
            }
        }

        // Add level 15 skills
        if (member.level >= 15) {
            switch (className) {
                case 'WARRIOR':
                    availableSkills.push({
                        ...CONSTANTS.SKILLS.LEVEL_15_SKILLS.MELEE,
                        level: 15,
                        requiresTarget: false,
                        targetType: 'all_enemies'
                    });
                    break;
                case 'MAGE':
                    availableSkills.push({
                        ...CONSTANTS.SKILLS.LEVEL_15_SKILLS.HEALING_LIGHT,
                        level: 15,
                        requiresTarget: false,
                        targetType: 'all_allies'
                    });
                    break;
                case 'RANGER':
                    availableSkills.push({
                        ...CONSTANTS.SKILLS.LEVEL_15_SKILLS.CRITICAL_SHOT,
                        level: 15,
                        requiresTarget: true,
                        targetType: 'enemy'
                    });
                    break;
            }
        }

        // Add level 20 skills
        if (member.level >= 20) {
            switch (className) {
                case 'WARRIOR':
                    availableSkills.push({
                        ...CONSTANTS.SKILLS.LEVEL_20_SKILLS.DRAGON_STRIKE,
                        level: 20,
                        requiresTarget: true,
                        targetType: 'enemy'
                    });
                    break;
                case 'MAGE':
                    availableSkills.push({
                        ...CONSTANTS.SKILLS.LEVEL_20_SKILLS.PROTECT,
                        level: 20,
                        requiresTarget: false,
                        targetType: 'all_allies'
                    });
                    break;
                case 'RANGER':
                    availableSkills.push({
                        ...CONSTANTS.SKILLS.LEVEL_20_SKILLS.SWIFT_MOVEMENT,
                        level: 20,
                        requiresTarget: false,
                        targetType: 'self'
                    });
                    break;
            }
        }

        return availableSkills;
    }

    // Execute a skill by ID
    executeSkill(skillId, caster, target = null) {
        switch (skillId) {
            case 'shield':
                return this.activateShield(caster);
            case 'exorcise':
                return this.activateExorcise(caster, target);
            case 'first_aid':
                return this.useFirstAid(caster, target);
            case 'triple_strike':
                return this.activateTripleStrike(caster, target);
            case 'silence':
                return this.activateSilence(caster);
            case 'hide':
                return this.activateHide(caster);
            case 'melee':
                return this.activateMelee(caster);
            case 'healing_light':
                return this.activateHealingLight(caster);
            case 'critical_shot':
                return this.activateCriticalShot(caster, target);
            case 'dragon_strike':
                return this.activateDragonStrike(caster, target);
            case 'protect':
                return this.activateProtect(caster);
            case 'swift_movement':
                return this.activateSwiftMovement(caster);
            default:
                return {
                    success: false,
                    message: `Unknown skill: ${skillId}`
                };
        }
    }

    // ===== LEVEL 10 SKILL IMPLEMENTATIONS =====

    // Activate TRIPLE STRIKE skill for WARRIOR
    activateTripleStrike(warrior, target) {
        if (!target || target.life <= 0) {
            return {
                success: false,
                message: `${warrior.name} cannot use TRIPLE STRIKE on an invalid target!`
            };
        }

        // This skill will be handled specially in combat system
        // Return success here, actual execution happens in combat
        return {
            success: true,
            message: `⚔️ ${warrior.name} prepares TRIPLE STRIKE against ${target.name}! ⚔️`,
            skillType: 'triple_strike',
            target: target
        };
    }

    // Activate SILENCE skill for MAGE
    activateSilence(mage) {
        // Apply silence to all enemies in combat
        if (window.combatSystem && combatSystem.enemies) {
            const silencedEnemies = [];
            combatSystem.enemies.forEach(enemy => {
                if (enemy.life > 0) {
                    const duration = this.randomBetween(
                        CONSTANTS.SKILLS.LEVEL_10_SKILLS.SILENCE.duration.min,
                        CONSTANTS.SKILLS.LEVEL_10_SKILLS.SILENCE.duration.max
                    );
                    enemy.silenced = true;
                    enemy.silenceTurnsLeft = duration;
                    silencedEnemies.push(enemy.name);
                }
            });

            if (silencedEnemies.length > 0) {
                return {
                    success: true,
                    message: `🔇 ${mage.name} casts SILENCE! ${silencedEnemies.join(', ')} cannot use magic! 🔇`
                };
            } else {
                return {
                    success: false,
                    message: `${mage.name} cannot cast SILENCE - no valid targets!`
                };
            }
        }

        return {
            success: false,
            message: `${mage.name} cannot cast SILENCE outside of combat!`
        };
    }

    // Activate HIDE skill for RANGER
    activateHide(ranger) {
        // Deactivate any existing hide effects first
        this.deactivateAllHide();

        ranger.hidden = true;
        ranger.hiddenTurnsLeft = CONSTANTS.SKILLS.LEVEL_10_SKILLS.HIDE.duration;

        return {
            success: true,
            message: `👤 ${ranger.name} activates HIDE! Enemies cannot target ${ranger.name} for ${ranger.hiddenTurnsLeft} turns! 👤`
        };
    }

    // Deactivate all hide effects
    deactivateAllHide() {
        gameState.party.forEach(member => {
            member.hidden = false;
            member.hiddenTurnsLeft = 0;
        });
    }

    // Get the current hidden character (if any)
    getHiddenCharacter() {
        return gameState.party.find(member => member.hidden && member.life > 0);
    }

    // ===== POISON STATUS MANAGEMENT =====

    // Apply poison to a character
    applyPoison(character, duration = 5) {
        character.poisoned = true;
        character.poisonTurnsLeft = duration;
        return {
            success: true,
            message: `💚 ${character.name} has been poisoned! 💚`
        };
    }

    // Cure poison from a character
    curePoison(character) {
        if (character.poisoned) {
            character.poisoned = false;
            character.poisonTurnsLeft = 0;
            return {
                success: true,
                message: `✨ ${character.name}'s poison has been cured! ✨`
            };
        }
        return {
            success: false,
            message: `${character.name} is not poisoned.`
        };
    }

    // Process poison damage during combat (per turn)
    processPoisonDamageCombat(character) {
        if (!character.poisoned || character.poisonTurnsLeft <= 0) {
            return null;
        }

        // Combat poison: 2-5 damage per turn
        const damage = Math.floor(Math.random() * 4) + 2; // 2-5 damage
        character.life = Math.max(0, character.life - damage);
        character.poisonTurnsLeft--;

        if (character.poisonTurnsLeft <= 0) {
            character.poisoned = false;
        }

        return {
            damage: damage,
            message: `💚 ${character.name} takes ${damage} poison damage! 💚`,
            cured: character.poisonTurnsLeft <= 0
        };
    }

    // Process poison damage during exploration (per step)
    processPoisonDamageExploration() {
        const poisonMessages = [];

        gameState.party.forEach(member => {
            if (member.poisoned && member.life > 0) {
                // Exploration poison: 1 damage per step
                member.life = Math.max(0, member.life - 1);
                poisonMessages.push(`💚 ${member.name} takes 1 poison damage! 💚`);

                // 2% chance to recover from poison each step
                if (Math.random() < 0.02) {
                    member.poisoned = false;
                    member.poisonTurnsLeft = 0;
                    poisonMessages.push(`✨ ${member.name}'s poison has naturally worn off! ✨`);
                }
            }
        });

        if (poisonMessages.length > 0) {
            gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
        }

        return poisonMessages;
    }

    // Check if character is poisoned
    isPoisoned(character) {
        return character.poisoned && character.poisonTurnsLeft > 0;
    }

    // Get all poisoned party members
    getPoisonedMembers() {
        return gameState.party.filter(member => this.isPoisoned(member));
    }

    // ===== PARALYSIS STATUS MANAGEMENT =====

    // Apply paralysis to a character
    applyParalysis(character, duration = 1) {
        character.paralyzed = true;
        character.paralysisTurnsLeft = duration;
        return {
            success: true,
            message: `⚡ ${character.name} has been paralyzed! ⚡`
        };
    }

    // Cure paralysis from a character
    cureParalysis(character) {
        if (character.paralyzed) {
            character.paralyzed = false;
            character.paralysisTurnsLeft = 0;
            return {
                success: true,
                message: `✨ ${character.name}'s paralysis has been cured! ✨`
            };
        }
        return {
            success: false,
            message: `${character.name} is not paralyzed.`
        };
    }

    // Check if a character is paralyzed
    isCharacterParalyzed(character) {
        return character.paralyzed && character.paralysisTurnsLeft > 0;
    }

    // Get all paralyzed party members
    getParalyzedMembers() {
        return gameState.party.filter(member => this.isCharacterParalyzed(member));
    }

    // ===== STATUS EFFECT MANAGEMENT =====

    // Process all status effects at end of turn
    processStatusEffects() {
        const results = {
            silenceExpired: [],
            hideExpired: [],
            paralysisExpired: [],
            incapacitationExpired: [],
            protectionExpired: [],
            swiftMovementExpired: []
        };

        // Process silence effects on enemies
        if (window.combatSystem && combatSystem.enemies) {
            combatSystem.enemies.forEach(enemy => {
                if (enemy.silenced && enemy.silenceTurnsLeft > 0) {
                    enemy.silenceTurnsLeft--;
                    if (enemy.silenceTurnsLeft <= 0) {
                        enemy.silenced = false;
                        results.silenceExpired.push(enemy.name);
                    }
                }
            });
        }

        // Process hide effects on party members
        gameState.party.forEach(member => {
            if (member.hidden && member.hiddenTurnsLeft > 0) {
                member.hiddenTurnsLeft--;
                if (member.hiddenTurnsLeft <= 0) {
                    member.hidden = false;
                    results.hideExpired.push(member.name);
                }
            }
        });

        // Process paralysis effects on party members
        gameState.party.forEach(member => {
            if (member.paralyzed && member.paralysisTurnsLeft > 0) {
                member.paralysisTurnsLeft--;
                if (member.paralysisTurnsLeft <= 0) {
                    member.paralyzed = false;
                    results.paralysisExpired.push(member.name);
                }
            }
        });

        // Process incapacitation effects on party members
        gameState.party.forEach(member => {
            if (member.incapacitated && member.incapacitationTurnsLeft > 0) {
                member.incapacitationTurnsLeft--;
                if (member.incapacitationTurnsLeft <= 0) {
                    member.incapacitated = false;
                    results.incapacitationExpired.push(member.name);
                }
            }
        });

        // Process protection effects on party members
        gameState.party.forEach(member => {
            if (member.protected && member.protectedTurnsLeft > 0) {
                member.protectedTurnsLeft--;
                if (member.protectedTurnsLeft <= 0) {
                    member.protected = false;
                    results.protectionExpired.push(member.name);
                }
            }
        });

        // Process swift movement effects on party members
        gameState.party.forEach(member => {
            if (member.swiftMovement && member.swiftMovementTurnsLeft > 0) {
                member.swiftMovementTurnsLeft--;
                if (member.swiftMovementTurnsLeft <= 0) {
                    member.swiftMovement = false;
                    results.swiftMovementExpired.push(member.name);
                }
            }
        });

        return results;
    }

    // Check if an enemy is silenced
    isEnemySilenced(enemy) {
        return enemy.silenced && enemy.silenceTurnsLeft > 0;
    }

    // Check if a character is hidden
    isCharacterHidden(character) {
        return character.hidden && character.hiddenTurnsLeft > 0;
    }

    // Check if a character is incapacitated
    isIncapacitated(character) {
        return character.incapacitated && character.incapacitationTurnsLeft > 0;
    }

    // Get targetable party members (excludes hidden characters)
    getTargetablePartyMembers() {
        return gameState.party.filter(member =>
            member.life > 0 && !this.isCharacterHidden(member)
        );
    }

    // ===== LEVEL 15 SKILL IMPLEMENTATIONS =====

    // Activate MELEE skill for WARRIOR
    activateMelee(warrior) {
        // Check if warrior has enough life to pay the cost
        const lifeCost = Math.floor(warrior.maxLife * CONSTANTS.SKILLS.LEVEL_15_SKILLS.MELEE.lifeCostRate);
        if (warrior.life <= lifeCost) {
            return {
                success: false,
                message: `${warrior.name} doesn't have enough life to use MELEE! (Requires ${lifeCost} life)`
            };
        }

        // Pay the life cost
        warrior.life = Math.max(1, warrior.life - lifeCost);

        // This skill will be handled specially in combat system for area damage
        // Return success here, actual execution happens in combat
        return {
            success: true,
            message: `⚔️ ${warrior.name} sacrifices ${lifeCost} life to unleash MELEE! All enemies will take massive damage! ⚔️`,
            skillType: 'melee',
            lifeCost: lifeCost
        };
    }

    // Activate HEALING_LIGHT skill for MAGE
    activateHealingLight(mage) {
        // Heal all party members by 30% of their max life
        const healedMembers = [];
        gameState.party.forEach(member => {
            if (member.life > 0 && member.life < member.maxLife) {
                const healingAmount = Math.floor(member.maxLife * CONSTANTS.SKILLS.LEVEL_15_SKILLS.HEALING_LIGHT.healingRate);
                const oldLife = member.life;
                member.life = Math.min(member.maxLife, member.life + healingAmount);
                const actualHealing = member.life - oldLife;

                if (actualHealing > 0) {
                    healedMembers.push({ name: member.name, amount: actualHealing });
                }
            }
        });

        // Apply incapacitation to the mage
        const duration = this.randomBetween(
            CONSTANTS.SKILLS.LEVEL_15_SKILLS.HEALING_LIGHT.incapacitationDuration.min,
            CONSTANTS.SKILLS.LEVEL_15_SKILLS.HEALING_LIGHT.incapacitationDuration.max
        );
        mage.incapacitated = true;
        mage.incapacitationTurnsLeft = duration;

        let message = `✨ ${mage.name} casts HEALING LIGHT! ✨\n`;
        if (healedMembers.length > 0) {
            healedMembers.forEach(healed => {
                message += `${healed.name} recovers ${healed.amount} life! `;
            });
        } else {
            message += 'All party members are already at full health! ';
        }
        message += `\n💫 ${mage.name} becomes incapacitated for ${duration} turns! 💫`;

        return {
            success: true,
            message: message,
            skillType: 'healing_light',
            healedMembers: healedMembers,
            incapacitationDuration: duration
        };
    }

    // Activate CRITICAL_SHOT skill for RANGER
    activateCriticalShot(ranger, target) {
        if (!target || target.life <= 0) {
            return {
                success: false,
                message: `${ranger.name} cannot use CRITICAL SHOT on an invalid target!`
            };
        }

        // Check if target is immune (has dragon property)
        if (target.properties && target.properties.includes('dragon')) {
            return {
                success: false,
                message: `💨 ${target.name} is immune to CRITICAL SHOT! Dragons cannot be instantly defeated! 💨`
            };
        }

        // This skill will be handled specially in combat system
        // Return success here, actual execution happens in combat
        return {
            success: true,
            message: `🎯 ${ranger.name} aims CRITICAL SHOT at ${target.name}! 🎯`,
            skillType: 'critical_shot',
            target: target
        };
    }

    // ===== LEVEL 20 SKILL IMPLEMENTATIONS =====

    // Activate DRAGON_STRIKE skill for WARRIOR
    activateDragonStrike(warrior, target) {
        if (!target || target.life <= 0) {
            return {
                success: false,
                message: `${warrior.name} cannot use DRAGON STRIKE on an invalid target!`
            };
        }

        // Check if warrior has enough life to pay the cost
        const lifeCost = Math.floor(warrior.maxLife * CONSTANTS.SKILLS.LEVEL_20_SKILLS.DRAGON_STRIKE.lifeCostRate);
        if (warrior.life <= lifeCost) {
            return {
                success: false,
                message: `${warrior.name} doesn't have enough life to use DRAGON STRIKE! (Requires ${lifeCost} life)`
            };
        }

        // Pay the life cost
        warrior.life = Math.max(1, warrior.life - lifeCost);

        // Check if target has dragon property for enhanced damage
        const isDragon = target.properties && target.properties.includes('dragon');
        const damageMultiplier = isDragon ? CONSTANTS.SKILLS.LEVEL_20_SKILLS.DRAGON_STRIKE.damageMultiplier : 1.0;

        // This skill will be handled specially in combat system
        // Return success here, actual execution happens in combat
        return {
            success: true,
            message: isDragon ?
                `🐉 ${warrior.name} sacrifices ${lifeCost} life to unleash DRAGON STRIKE! ${target.name} will take massive damage! 🐉` :
                `⚔️ ${warrior.name} sacrifices ${lifeCost} life to use DRAGON STRIKE against ${target.name}! ⚔️`,
            skillType: 'dragon_strike',
            target: target,
            lifeCost: lifeCost,
            damageMultiplier: damageMultiplier,
            isDragon: isDragon
        };
    }

    // Activate PROTECT skill for MAGE
    activateProtect(mage) {
        // Apply protection to all party members
        const duration = this.randomBetween(
            CONSTANTS.SKILLS.LEVEL_20_SKILLS.PROTECT.duration.min,
            CONSTANTS.SKILLS.LEVEL_20_SKILLS.PROTECT.duration.max
        );

        const protectedMembers = [];
        gameState.party.forEach(member => {
            if (member.life > 0) {
                member.protected = true;
                member.protectedTurnsLeft = duration;
                protectedMembers.push(member.name);
            }
        });

        // Apply incapacitation to the mage
        const incapacitationDuration = this.randomBetween(
            CONSTANTS.SKILLS.LEVEL_20_SKILLS.PROTECT.incapacitationDuration.min,
            CONSTANTS.SKILLS.LEVEL_20_SKILLS.PROTECT.incapacitationDuration.max
        );
        mage.incapacitated = true;
        mage.incapacitationTurnsLeft = incapacitationDuration;

        let message = `🛡️ ${mage.name} casts PROTECT! All party members' physical defense is doubled for ${duration} turns! 🛡️\n`;
        message += `💫 ${mage.name} becomes incapacitated for ${incapacitationDuration} turns! 💫`;

        return {
            success: true,
            message: message,
            skillType: 'protect',
            protectedMembers: protectedMembers,
            duration: duration,
            incapacitationDuration: incapacitationDuration
        };
    }

    // Activate SWIFT_MOVEMENT skill for RANGER
    activateSwiftMovement(ranger) {
        // Apply swift movement effect to the ranger
        const duration = this.randomBetween(
            CONSTANTS.SKILLS.LEVEL_20_SKILLS.SWIFT_MOVEMENT.duration.min,
            CONSTANTS.SKILLS.LEVEL_20_SKILLS.SWIFT_MOVEMENT.duration.max
        );

        ranger.swiftMovement = true;
        ranger.swiftMovementTurnsLeft = duration;

        return {
            success: true,
            message: `💨 ${ranger.name} activates SWIFT MOVEMENT! Agility is doubled for ${duration} turns! 💨`,
            skillType: 'swift_movement',
            duration: duration
        };
    }
}

// Create global instance
const characterSystem = new Character();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.characterSystem = characterSystem;
}
